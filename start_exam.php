<?php
$servername = "localhost";
$username = "smartexa_system";
$password = "mostafa900";
$dbname = "smartexa_system";

// إنشاء اتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

$id_ujian = $_GET['id_ujian'];
$sql = "SELECT nama_ujian FROM m_ujian WHERE id_ujian = $id_ujian";
$result = $conn->query($sql);
$exam_name = $result->fetch_assoc()['nama_ujian'];

$sql = "SELECT questions FROM mo WHERE id_ujian = $id_ujian";
$result = $conn->query($sql);
$questions_list = explode(',', $result->fetch_assoc()['questions']);
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>الامتحان: <?php echo $exam_name; ?></title>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f0f8ff;
            direction: rtl;
            text-align: right;
        }
        .container {
            margin-top: 50px;
        }
        .question-box {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fff;
        }
        .btn-custom {
            background-color: #4CAF50;
            color: white;
        }
        .btn-custom:hover {
            background-color: #45a049;
        }
        .btn-danger {
            background-color: red;
            color: white;
        }
    </style>
</head>
<body>

<div class="container">
    <h2 class="text-center">الامتحان: <?php echo $exam_name; ?></h2>
    <div id="questions-container">
        <?php
        foreach ($questions_list as $index => $question_id) {
            $sql = "SELECT * FROM tb_soal WHERE id_soal = $question_id";
            $result = $conn->query($sql);
            $question_data = $result->fetch_assoc();
            
            echo "<div class='question-box' id='question-".$index."'>";
            echo "<h4>السؤال ".($index + 1).": ".$question_data['soal']."</h4>";
            echo "<div class='form-check'><input class='form-check-input' type='radio' name='answer-$index' id='answer-$index-A' value='A'><label class='form-check-label' for='answer-$index-A'>".$question_data['opsi_A']."</label></div>";
            echo "<div class='form-check'><input class='form-check-input' type='radio' name='answer-$index' id='answer-$index-B' value='B'><label class='form-check-label' for='answer-$index-B'>".$question_data['opsi_B']."</label></div>";
            echo "<div class='form-check'><input class='form-check-input' type='radio' name='answer-$index' id='answer-$index-C' value='C'><label class='form-check-label' for='answer-$index-C'>".$question_data['opsi_C']."</label></div>";
            echo "<div class='form-check'><input class='form-check-input' type='radio' name='answer-$index' id='answer-$index-D' value='D'><label class='form-check-label' for='answer-$index-D'>".$question_data['opsi_D']."</label></div>";
            echo "</div>";
        }
        ?>
    </div>
    <div class="text-center">
        <button class="btn btn-danger" onclick="submitExam()">انهاء الإمتحان</button>
    </div>
</div>

<script>
function submitExam() {
    var answers = [];
    <?php
    foreach ($questions_list as $index => $question_id) {
        echo "answers.push({id: $question_id, answer: document.querySelector('input[name=\"answer-$index\"]:checked') ? document.querySelector('input[name=\"answer-$index\"]:checked').value : null});\n";
    }
    ?>

    var xhttp = new XMLHttpRequest();
    xhttp.open("POST", "save_exam.php", true);
    xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhttp.send("id_ujian=<?php echo $id_ujian; ?>&answers=" + JSON.stringify(answers));

    xhttp.onreadystatechange = function() {
        if (this.readyState == 4 && this.status == 200) {
            alert("تم حفظ الامتحان بنجاح!");
            window.location.href = "view_result.php?id_ujian=<?php echo $id_ujian; ?>";
        }
    };
}
</script>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>

<?php
$conn->close();
?>
 
