<!DOCTYPE html>
<html>
<head>
<style>
table {
  font-family: Arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

td, th {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

th {
  background-color: #4CAF50;
  color: white;
}


</style>
</head>
<body>
<?php if( $this->ion_auth->is_admin() ) : ?>
<div class="row">
    <?php foreach($info_box as $info) : ?>
    <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-<?=$info->box?>">
        <div class="inner">
            <h3><?=$info->total;?></h3>
            <p><?=$info->text;?></p>
          

        </div>
        <div class="icon">
            <i class="fa fa-<?=$info->icon?>"></i>
        </div>
        <a href="<?=base_url().strtolower($info->title);?>" class="small-box-footer">
            More info <i class="fa fa-arrow-circle-right"></i>
        </a>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<?php elseif( $this->ion_auth->in_group('Lecturer') ) : ?>

<div class="row">
    <div class="col-sm-4">
        <div class="box box-default">
            <div class="box-header with-border">
                <h3 class="box-title">معلومات الحساب</h3>
            </div>
            <table class="table table-hover">
                <tr>
                    <th>الاسم</th>
                    <td><?=$dosen->nama_dosen?></td>
                </tr>
                <tr>
                    <th>IP</th>
                    <td><?=$dosen->nip?></td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td><?=$dosen->email?></td>
                </tr>
                <tr>
                    <th>المقرر</th>
                    <td><?=$dosen->nama_matkul?></td>
                </tr>
                <tr>
                    <th>الفصل</th>
                    <td>
                        <ol class="pl-4">
                        <?php foreach ($kelas as $k) : ?>
                            <li><?=$k->nama_kelas?></li>
                        <?php endforeach;?>
                        </ol>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<?php
// معلومات الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smartexa_system";
$password = "mostafa900";
$dbname = "smartexa_system";

// إنشاء اتصال بقاعدة البيانات
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

// تحديد قيمة المتغير $dosen->nip من بيانات الجلسة أو من أي مصدر آخر
$dosen_nip = $dosen->nip; // تخمين للغرض التوضيحي

// استعلام SQL لاسترداد القيم من جدول users
$query = "SELECT `in`, `out` FROM `users` WHERE `username` = '$dosen_nip'";
$result = $conn->query($query);

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $in = $row['in'];
    $out = $row['out'];

    if (empty($in)) {
        header("Location: charge.php"); // إعادة توجيه المستخدم إلى صفحة charge.php إذا كان $in فارغًا
        exit();
    }
} else {
    echo "لم يتم العثور على نتائج.";
    exit();
}

// إغلاق الاتصال بقاعدة البيانات
$conn->close();
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحقق من عملية الدفع</title>
    <style>
        
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 300px;
        }
        .container p {
            margin: 10px 0;
            font-size: 18px;
        }
        .amount {
            font-weight: bold;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <p>المبلغ المتخزن: <span class="amount"><?php echo $in; ?></span></p>
        <p>المبلغ المخصوم: <span class="amount"><?php echo $out; ?></span></p>
    </div>
</body>
</html>


<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        button {
            background-color: #4ca1af;
            color: white;
            padding: 15px 20px;
            margin: 20px 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 18px;
        }
        button:hover {
            background-color: #357a81;
        }
        .button-container {
            display: flex;
            justify-content: space-between;
        }
    </style>
</head>
<body>
    <div class="button-container">
        <form method="post" action="students.php">
            <input type="hidden" name="ml" value="<?php echo htmlspecialchars($k->id_kelas); ?>">
            <button type="submit">اضافة طلبة</button>
        </form>
        <form method="post" action="student.php">
            <input type="hidden" name="mll" value="<?php echo htmlspecialchars($k->id_kelas); ?>">
            <button type="submit">عرض الطلبة</button>
        </form>
        <form method="post" action="details.php">
            <input type="hidden" name="ml" value="<?php echo htmlspecialchars($k->id_kelas); ?>">
            <input type="hidden" name="mk" value="<?php echo htmlspecialchars($dosen->id_dosen); ?>">
            <button type="submit">الاطلاع علي الاحصائيات</button>
        </form>
    </div>
</body>
</html>


<?php
// اتصال بقاعدة البيانات
$servername = "localhost";
$username = "smarte10_exam";
$password = "mostafa900";
$dbname = "smarte10_exam";

$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

// تعيين ترميز الحروف
mysqli_set_charset($conn, "utf8");

// استعلام SQL لجلب البيانات
$sql = "
    SELECT m.nama AS nama_mahasiswa, h.mahasiswa_id, SUM(h.nilai) AS total_nilai,
           RANK() OVER (ORDER BY SUM(h.nilai) DESC) AS ranking
    FROM h_ujian h
    INNER JOIN mahasiswa m ON h.mahasiswa_id = m.id_mahasiswa
    WHERE m.kelas_id = '$k->kelas_id'
    GROUP BY h.mahasiswa_id, m.nama
    ORDER BY total_nilai DESC
    LIMIT 10
";


$result = $conn->query($sql);

// عرض البيانات في جدول HTML
if ($result->num_rows > 0) {
	echo '<p><strong>لوحة الشرف لهذا الصف</strong></p>'; 
    echo "<table><tr><th>مجموع الدرجات</th><th>اسم الطالب</th><th>الترتيب</th></tr>";
    // عرض البيانات لكل صف في النتيجة
    while($row = $result->fetch_assoc()) {
        echo "<tr><td>" . $row["total_nilai"] . "</td><td>" . $row["nama_mahasiswa"]. "</td><td>" . $row["ranking"]. "</td></tr>";
    }
    echo "</table>";
} else {
    echo "ليست هناك امتحانات لهذا الصف";
}
$conn->close();
?>

<?php else : ?>

<div class="row">
    <div class="col-sm-4">
        <div class="box box-default">
            <div class="box-header with-border">
                <h3 class="box-title">معلومات الحساب</h3>
            </div>
            <table class="table table-hover">
                <tr>
                    <th>IP</th>
                    <td><?=$mahasiswa->nim?></td>
                </tr>
                
                <tr>
                    <th>الاسم</th>
                    <td><?=$mahasiswa->nama?></td>
                    </tr>
                
                <tr>
 

                  <th>Email</th>
                    <td><?=$mahasiswa->email?></td>
                </tr>
                <tr>
                    <th>القسم</th>
                    <td><?=$mahasiswa->nama_jurusan?></td>
                </tr>
                <tr>
                    <th>الفصل</th>
                    <td><?=$mahasiswa->nama_kelas?></td>
                </tr>
            </table>
        </div>
    </div>
    </div>
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استرجاع الحساب</title>
    <style>
    
        .container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #4ca1af;
        }
        p {
            font-size: 18px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4ca1af;
            color: white;
            padding: 15px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 18px;
        }
        button:hover {
            background-color: #357a81;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <p>الزر ده لو حصل معاك مشكلة انهاء الامتحان و درجة الإمتحان متحفظتش ف ده هيحسبهالك من جديد و هتظهرلك</p>
        <form method="post" action="errexam.php">
            <input type="hidden" name="mp" value="<?php echo htmlspecialchars($mahasiswa->id_mahasiswa); ?>">
            <button type="submit">إعادة حساب</button>
        </form>
    </div>
</body>
</html>


<?php
// بيانات الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smarte10_exam";
$password = "mostafa900";
$dbname = "smarte10_exam";

// إنشاء اتصال بقاعدة البيانات
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// الحصول على بيانات الطالب
$mahasiswa_nim = $conn->real_escape_string($mahasiswa->nim);
$mahasiswa_nam = $conn->real_escape_string($mahasiswa->nama);

// الحصول على User Agent
$user_agent = $_SERVER['HTTP_USER_AGENT'];

// استخراج اسم الجهاز من الـ user agent باستخدام تعبيرات منتظمة
function extractDeviceName($user_agent) {
    if (preg_match('/\((.*?)\)/', $user_agent, $matches)) {
        return $matches[1];
    }
    return 'Unknown Device';
}

// التحقق إذا كانت البيانات موجودة في جدول mahasiswa
$sql = "SELECT * FROM mahasiswa WHERE nim = '$mahasiswa_nim' AND nama = '$mahasiswa_nam'";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // البيانات موجودة في جدول mahasiswa
    // عرض رسالة للمستخدم
    echo "<script type='text/javascript'>
            alert('غير مسموح بدخول الطلاب (سوف يتم التوجيه الي صفحة الطلاب) ');
            window.location.href = 'http://student.smartexams.whf.bz/';
          </script>";

    // إلغاء الجلسة للطالب
    session_start();
    session_unset(); // إزالة كافة المتغيرات
    session_destroy(); // تدمير الجلسة
    exit(); // إنهاء السكربت لتوجيه المستخدم
} else {
    // البيانات غير موجودة في جدول mahasiswa
    echo "البيانات غير موجودة في النظام.";
}

$conn->close();
?>

$device_name = extractDeviceName($user_agent);

// التحقق من وجود الجهاز بالفعل في قاعدة البيانات بناءً على device_id
$sql_check_device_id = "SELECT * FROM devices WHERE device_id = '$mahasiswa_nim'";
$result_device_id = $conn->query($sql_check_device_id);

if ($result_device_id->num_rows > 0) {
    // الجهاز موجود بالفعل، التحقق من البيانات
    $row_device = $result_device_id->fetch_assoc();
    if ($row_device['user_agent'] == $device_name) {
        echo "";
    } else {
        // توجيه إلى صفحة warning.php
        header("Location: warning.php");
        exit();
    }
} else {
    // الجهاز غير موجود، إضافة البيانات الجديدة
    $sql_insert = "INSERT INTO devices (name, device_id, user_agent) VALUES ('$mahasiswa_nam','$mahasiswa_nim', '$device_name')";
    if ($conn->query($sql_insert) === TRUE) {
        echo "";
    } else {
        echo "Error: " . $sql_insert . "<br>" . $conn->error;
    }
}

// إغلاق الاتصال
$conn->close();
?>







<?php
// الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smartexa_system";
$password = "mostafa900";
$dbname = "smartexa_system";

$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من اتصال قاعدة البيانات
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

// تحديد ترميز النصوص كـ UTF-8
header('Content-Type: text/html; charset=utf-8');

// تحديد ترميز اتصال قاعدة البيانات كـ UTF-8
$conn->set_charset("utf8");

// بيانات الطالب
$mahasiswa_nama = $conn->real_escape_string($mahasiswa->nama);
$mahasiswa_nim = $conn->real_escape_string($mahasiswa->nim);
$mahasiswa_email = $conn->real_escape_string($mahasiswa->email);

// استعلام SQL لجلب البيانات مع الانضمام إلى جدول m_ujian و mahasiswa
$sql = "SELECT *
        FROM h_ujian h
        JOIN m_ujian m ON h.ujian_id = m.id_ujian
        JOIN mahasiswa mah ON h.mahasiswa_id = mah.id_mahasiswa
        WHERE mah.nama = '$mahasiswa_nama' AND mah.nim = '$mahasiswa_nim' AND mah.email = '$mahasiswa_email'";

$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo '<p><strong>قسم النتائج الفورية للامتحانات</strong></p>'; 
    // عرض البيانات في جدول HTML مع إضافة عمود جديد لعرض الـ coins
    echo "<table><tr><th>اسم الامتحان</th><th>درجتك</th><th>الدرجة الكلية للامتحان</th></tr>";
    while($row = $result->fetch_assoc()) {
        if ($row["jml_benar"] != 0) {
            $coins = ($row["nilai"] / $row["jml_benar"]) * 100; // النسبة المئوية
        } else {
            $coins = 0; // تعيين قيمة الـ coins إلى صفر إذا كان jml_benar يساوي صفر
        }

        // عرض البيانات في الجدول
        echo "<tr><td>" . $row["nama_ujian"]. "</td><td>" . $row["nilai"]. "</td><td>" . $row["jml_benar"]. "</td></tr>";
    }
    echo "</table>";
} else {
    echo "لا توجد نتائج امتحانات حتي الآن ";
}

// إغلاق اتصال قاعدة البيانات
$conn->close();
?>	


<?php endif; ?>               