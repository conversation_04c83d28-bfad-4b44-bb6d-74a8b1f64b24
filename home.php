<?php
// إعداد الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smartexa_system";
$password = "mostafa900";
$dbname = "smartexa_system";

// إنشاء اتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// جلب البيانات من جدول matkul
$sql = "SELECT id_matkul, nama_matkul FROM matkul";
$result = $conn->query($sql);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matkul List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }
        .card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 200px;
            text-align: center;
        }
        .card h2 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        .card button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .card button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>

<?php
if ($result->num_rows > 0) {
    // عرض البيانات في مربعات
    while($row = $result->fetch_assoc()) {
        echo '<div class="card">';
        echo '<h2>' . $row["nama_matkul"] . '</h2>';
        echo '<a href="login?index=' . $row["id_matkul"] . '"><button>Go to Login</button></a>';
        echo '</div>';
    }
} else {
    echo "0 results";
}
$conn->close();
?>

</body>
</html>
