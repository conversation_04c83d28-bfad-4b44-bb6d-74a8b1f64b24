<?php
// إعداد الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smartexa_system";
$password = "mostafa900";
$dbname = "smartexa_system";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);
$conn->set_charset("utf8");
// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

if (isset($_POST['ids'])) {
    $lectureIds = json_decode($_POST['ids']);
    if (count($lectureIds) > 0) {
        $placeholders = implode(',', array_fill(0, count($lectureIds), '?'));
        $sql = "SELECT id_soal, soal FROM tb_soal WHERE mada_id IN ($placeholders)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param(str_repeat('i', count($lectureIds)), ...$lectureIds);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            echo '<option value="' . $row['id_soal'] . '">' . $row['soal'] . '</option>';
        }
        
        $stmt->close();
    }
}

// إغلاق الاتصال بقاعدة البيانات
$conn->close();
?>
