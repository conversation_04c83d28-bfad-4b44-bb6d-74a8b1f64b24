<?php
function extractQuestions($text, $mada_id, $mada_nam) {
    $questions = [];
    // تعديل النمط ليشمل الخيارات من A إلى J
    $pattern = '/(\d+)\s*[\/\.\-\)]\s*(.*?)\s*[aA]\s*[\/\.\-\)]\s*(.*?)\s*(?:[bB]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[cC]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[dD]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[eE]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[fF]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[gG]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[hH]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[iI]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:[jJ]\s*[\/\.\-\)]\s*(.*?)\s*)?(?:Answer|answer)\s*[\/\:\.]\s*(.*?)\s*(?:ex[\/\:\.]\s*(.*?))?(?=\d+\s*[\/\.\-\)]|\Z)/s';

    preg_match_all($pattern, $text, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $questions[] = [
            'id_question' => trim($match[1]),
            'mada_id' => $mada_id,
            'soal' => trim($match[2]),
            'a' => isset($match[3]) ? trim($match[3]) : '',
            'b' => isset($match[4]) ? trim($match[4]) : '',
            'c' => isset($match[5]) ? trim($match[5]) : '',
            'd' => isset($match[6]) ? trim($match[6]) : '',
            'e' => isset($match[7]) ? trim($match[7]) : '',
            'f' => isset($match[8]) ? trim($match[8]) : '',
            'g' => isset($match[9]) ? trim($match[9]) : '',
            'h' => isset($match[10]) ? trim($match[10]) : '',
            'i' => isset($match[11]) ? trim($match[11]) : '',
            'j' => isset($match[12]) ? trim($match[12]) : '',
            'jawaban' => strtoupper(trim($match[13])),
            'ex' => isset($match[14]) ? trim($match[14]) : '', // يتم تعيينه إلى سلسلة فارغة إذا لم يكن موجودًا
            'bobot' => 1,
            'dosen_id' => 16,
            'matkul_id' => 13,
            'mada_nam' => $mada_nam, // تضمين mada_name في المصفوفة
        ];
    }

    return $questions;
}

function insertQuestionsIntoDB($questions) {
    $dsn = 'mysql:host=localhost;dbname=smartexa_system;charset=utf8';
    $username = 'smartexa_system';
    $password = 'mostafa900';

    try {
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare('INSERT INTO tb_soal (id_question, mada_id, soal, A, B, C, D, E, F, G, H, I, J, jawaban, ex, bobot, dosen_id, matkul_id, mada_nam) VALUES (:id_question, :mada_id, :soal, :a, :b, :c, :d, :e, :f, :g, :h, :i, :j, :jawaban, :ex, :bobot, :dosen_id, :matkul_id, :mada_nam)');

        foreach ($questions as $question) {
            try {
                $stmt->execute([
                    'id_question' => $question['id_question'],
                    'mada_id' => $question['mada_id'],
                    'soal' => $question['soal'],
                    'a' => $question['a'],
                    'b' => $question['b'],
                    'c' => $question['c'],
                    'd' => $question['d'],
                    'e' => $question['e'],
                    'f' => $question['f'],
                    'g' => $question['g'],
                    'h' => $question['h'],
                    'i' => $question['i'],
                    'j' => $question['j'],
                    'jawaban' => $question['jawaban'],
                    'ex' => $question['ex'],
                    'bobot' => $question['bobot'],
                    'dosen_id' => $question['dosen_id'],
                    'matkul_id' => $question['matkul_id'],
                    'mada_nam' => $question['mada_nam'], // Bind mada_name to the query
                ]);
                echo "Question ID " . $question['id_question'] . " has been successfully inserted!<br>";
            } catch (PDOException $e) {
                echo "Failed to insert question ID " . $question['id_question'] . ": " . $e->getMessage() . "<br>";
                echo "Question details: " . json_encode($question) . "<br>";
            }
        }

    } catch (PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['questions']) && isset($_POST['mada_id']) && isset($_POST['mada_nam'])) {
    $text = $_POST['questions'];
    $mada_id = $_POST['mada_id'];
    $mada_nam = $_POST['mada_nam'];

    // Extract questions from the text
    $questions = extractQuestions($text, $mada_id, $mada_nam);

    // Insert questions into the database
    insertQuestionsIntoDB($questions);
} else {
    echo "No text provided, no subject selected, or invalid request method.";
}
?>