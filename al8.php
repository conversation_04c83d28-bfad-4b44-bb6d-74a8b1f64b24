<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Questions</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #495057;
        }
        .container {
            background: #ffffff;
            padding: 30px 40px;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 700px;
            width: 100%;
        }
        h1 {
            margin-bottom: 25px;
            font-size: 28px;
            color: #343a40;
            text-align: center;
        }
        label {
            font-size: 16px;
            color: #343a40;
        }
        textarea, select {
            width: 100%;
            padding: 12px;
            margin-top: 8px;
            margin-bottom: 20px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 16px;
            resize: vertical;
        }
        input[type="submit"] {
            background: #007bff;
            color: #ffffff;
            border: none;
            padding: 12px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: block;
            margin: 0 auto;
        }
        input[type="submit"]:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Insert Questions</h1>
        <form action="process8.php" method="post">
            <label for="mada_id">المادة</label>
            <select name="mada_id" required="required" id="mada_id" class="select2 form-group">
                <option value="" disabled selected>اختار المادة</option>
                <?php
                // Database connection details
                $host = 'localhost';
                $db   = 'smartexa_system';
                $user = 'smartexa_system';
                $pass = 'mostafa900';

                // Connect to the database
                $conn = new mysqli($host, $user, $pass, $db);

                // Check connection
                if ($conn->connect_error) {
                    die("Connection failed: " . $conn->connect_error);
                }

                // Fetch subjects
                $dosen_id = 8; // Adjust this to the actual $dosen->id_dosen value
                $query = "SELECT id, Mada FROM Madas WHERE dosen_id = $dosen_id";
                $result = $conn->query($query);

                if ($result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        echo '<option value="'.$row['id'].'">'.$row['Mada'].'</option>';
                    }
                } else {
                    echo '<option value="" disabled>لا توجد مواد متاحة</option>';
                }

                $conn->close();
                ?>
            </select>
            <input type="hidden" id="mada_nam" name="mada_nam">

            <label for="questions">Enter questions and answers:</label>
            <textarea name="questions" id="questions" rows="20" required></textarea>
            <input type="submit" value="Submit">
        </form>
    </div>

    <script>
        // Display selected Mada name automatically
        var selectMada = document.getElementById('mada_id');
        var madaNameInput = document.getElementById('mada_nam');

        selectMada.addEventListener('change', function() {
            var selectedOption = selectMada.options[selectMada.selectedIndex];
            madaNameInput.value = selectedOption.textContent;
        });
    </script>
</body>
</html>
