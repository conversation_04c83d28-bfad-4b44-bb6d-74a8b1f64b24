<?php
// معلومات الاتصال بقاعدة البيانات
$host = 'localhost';
$db   = 'smartexa_system';
$user = 'smartexa_system';
$pass = 'mostafa900';

// الاتصال بقاعدة البيانات
$conn = new mysqli($host, $user, $pass, $db);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// التحقق مما إذا كان النموذج قد أرسل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add'])) {
        $Mada = $_POST['Mada'];
        $dosen_id = 15;

        // إعداد الاستعلام
        $stmt = $conn->prepare("INSERT INTO Madas (Mada, dosen_id) VALUES (?, ?)");
        $stmt->bind_param("si", $Mada, $dosen_id);

        // تنفيذ الاستعلام
        if ($stmt->execute()) {
            echo "<div class='success'>تمت إضافة المحاضرة بنجاح!</div>";
        } else {
            echo "<div class='error'>حدث خطأ أثناء إضافة المحاضرة: " . $stmt->error . "</div>";
        }

        $stmt->close();
    } elseif (isset($_POST['delete'])) {
        $id = $_POST['id'];

        // حذف المادة
        $stmt = $conn->prepare("DELETE FROM Madas WHERE id = ?");
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            echo "<div class='success'>تم حذف المحاضرة بنجاح!</div>";
        } else {
            echo "<div class='error'>حدث خطأ أثناء حذف المحاضرة: " . $stmt->error . "</div>";
        }

        $stmt->close();
    } elseif (isset($_POST['edit'])) {
        $id = $_POST['id'];
        $Mada = $_POST['Mada'];

        // تعديل المادة
        $stmt = $conn->prepare("UPDATE Madas SET Mada = ? WHERE id = ?");
        $stmt->bind_param("si", $Mada, $id);

        if ($stmt->execute()) {
            echo "<div class='success'>تم تعديل المحاضرة بنجاح!</div>";
        } else {
            echo "<div class='error'>حدث خطأ أثناء تعديل المحاضرة: " . $stmt->error . "</div>";
        }

        $stmt->close();
    }
}

// جلب البيانات من الجدول Madas
$sql = "SELECT * FROM Madas WHERE dosen_id = 15";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المحاضرات</title>
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(to right, #ece9e6, #ffffff);
            margin: 0;
            padding: 20px;
            color: #333;
        }

        h1 {
            text-align: center;
            color: #0056b3;
            margin-bottom: 30px;
        }

        form {
            background: #fff;
            padding: 5px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 0 auto 30px auto;
        }

        form label {
            display: block;
            margin-bottom: 10px;
            color: #0056b3;
        }

        form input[type="text"] {
            width: 90%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        form button {
            background: #0056b3;
            color: #fff;
            padding: 8px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 10%;
        }

        form button:hover {
            background: #003d80;
        }

        .success {
            background: #dff0d8;
            color: #3c763d;
            padding: 10px;
            border: 1px solid #d6e9c6;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        .error {
            background: #f2dede;
            color: #a94442;
            padding: 10px;
            border: 1px solid #ebccd1;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th, td {
            padding: 15px;
            text-align: left;
        }

        th {
            background: #f5f5f5;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #f1f1f1;
        }

        .delete-button {
            background: #e74c3c;
            color: #fff;
            padding: 5px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .delete-button:hover {
            background: #c0392b;
        }

        .edit-button {
            background: #3498db;
            color: #fff;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .edit-button:hover {
            background: #2980b9;
        }

        @media (max-width: 600px) {
            form, table {
                width: 100%;
                box-shadow: none;
            }

            form button {
                width: 100%;
            }

            th, td {
                padding: 10px;
                font-size: 14px;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <h1>إدارة المحاضرات</h1>
    <form method="POST">
        <label for="Mada">اسم المحاضرة</label>
        <input type="text" id="Mada" name="Mada" required>
        <button type="submit" name="add">إرسال</button>
    </form>

    <h2>قائمة المحاضرات</h2>
    <table>
        <tr>
            <th>الرقم</th>
            <th>اسم المادة</th>
            <th>الإجراءات</th>
        </tr>
        <?php
        if ($result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . $row['Mada'] . "</td>";
                echo "<td>";
                echo "<form method='POST' style='display:inline;'>";
                echo "<input type='hidden' name='id' value='" . $row['id'] . "'>";
                echo "<button type='submit' name='delete' class='delete-button'>حذف</button>";
                echo "</form>";
                echo " ";
                echo "<button onclick='editLecture(" . $row['id'] . ", \"" . $row['Mada'] . "\")' class='edit-button'>تعديل</button>";
                echo "</td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='3'>لا توجد محاضرات حاليا</td></tr>";
        }
        ?>
    </table>

    <!-- نموذج تعديل المحاضرة -->
    <div id="editModal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%, -50%); background:#fff; padding:20px; border-radius:8px; box-shadow:0 0 10px rgba(0,0,0,0.1);">
        <h2>تعديل المحاضرة</h2>
        <form method="POST">
            <input type="hidden" id="editId" name="id">
            <label for="editMada">اسم المحاضرة</label>
            <input type="text" id="editMada" name="Mada" required>
            <button type="submit" name="edit">حفظ التعديلات</button>
          
            <button type="button" onclick="closeEditModal()">إلغاء</button>
        </form>
    </div>

    <!-- الكود السابق للأزرار -->
    <button onclick="redirectToExamPage()" style="position: fixed; bottom: 20px; right: 20px; background-color: #0056b3; color: #fff; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer;">الرجوع</button>

    <script>
        function editLecture(id, mada) {
            document.getElementById('editId').value = id;
            document.getElementById('editMada').value = mada;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        function redirectToExamPage() {
            window.location.href = "http://smartexams.whf.bz/soal";
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>
