<?php
// تعريف معلومات الاتصال بقاعدة البيانات
$servername = "localhost";
$username = "smarte10_exam";
$password = "mostafa900";
$dbname = "smarte10_exam";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}
$conn->set_charset("utf8");
// تعريف المتغيرات $matkul_id و $dosen_id (المفترض أنهما معرفات معلمة)
$matkul_id = $matkul->matkul_id; // Example value
$dosen_id = $dosen->id_dosen; // Example value


// استعلام لاسترجاع بيانات الامتحانات
$sql_exams = "SELECT * FROM m_ujian WHERE matkul_id = ? AND dosen_id = ?";
$stmt_exams = $conn->prepare($sql_exams);

// ربط المعلمات بالاستعلام
$stmt_exams->bind_param("ii", $matkul_id, $dosen_id);

// تنفيذ الاستعلام
$stmt_exams->execute();

// الحصول على نتيجة الاستعلام
$result_exams = $stmt_exams->get_result();

// تهيئة مصفوفة لتخزين البيانات المسترجعة
$exams = [];
if ($result_exams->num_rows > 0) {
    while ($row = $result_exams->fetch_assoc()) {
        $exams[] = $row;
    }
}



// استعلام لاسترجاع بيانات المحاضرات
// إنشاء اتصال بقاعدة البيانات
$conn_lectures = new mysqli($servername, $username, $password, $dbname);

// التحقق من اتصال قاعدة البيانات
if ($conn_lectures->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn_lectures->connect_error);
}

// تعيين ترميز الاتصال إلى UTF-8
$conn_lectures->set_charset("utf8");

// قيمة المثال لمعرف المدرس
$dosen_id = $dosen->id_dosen; // قيمة المثال

// استعلام SQL لاسترداد السجلات
$sql_lectures = "SELECT id, Mada FROM Madas WHERE dosen_id = ?";
$stmt = $conn_lectures->prepare($sql_lectures);

// ربط قيمة معرف المدرس بالاستعلام
$stmt->bind_param("s", $dosen_id);

// تنفيذ الاستعلام
$stmt->execute();

// الحصول على نتائج الاستعلام
$result_lectures = $stmt->get_result();

// مصفوفة لتخزين السجلات
$lectures = [];

// التحقق من وجود نتائج
if ($result_lectures->num_rows > 0) {
    while($row = $result_lectures->fetch_assoc()) {
        $lectures[] = $row;
    }
}

// إغلاق الاستعلام والاتصال بقاعدة البيانات
$stmt->close();
$conn_lectures->close();

// استخدام المصفوفة $lectures كما تحتاج
?>


<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>قوائم منسدلة متعددة الاختيارات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            padding: 20px;
        }
        form {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        label, select, input[type="submit"] {
            display: block;
            margin-bottom: 10px;
        }
        select {
            width: 100%;
            padding: 10px;
            font-size: 16px;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
        
        .back-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            text-align: center;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .back-button:hover {
            background-color: #45a049;
        } 
    </style>
    <script>
        function fetchQuestions() {
            var selectedLectures = [];
            var lectureOptions = document.getElementById('lecture').options;
            for (var i = 0; i < lectureOptions.length; i++) {
                if (lectureOptions[i].selected) {
                    selectedLectures.push(lectureOptions[i].value);
                }
            }

            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://smartexams.whf.bz/fetch_questions.php', true);
            xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function () {
                if (xhr.readyState == 4 && xhr.status == 200) {
                    document.getElementById('questions').innerHTML = xhr.responseText;
                }
            };
            xhr.send('ids=' + JSON.stringify(selectedLectures));
        }
    </script>
</head>
<body>

<form action="n.php" method="POST">
    <div class="form-group">
        <label for="Exams">اختار الامتحان او الامتحانات (في حالة اذا كنت تريد اضافة نفس الاسئلة لاكتر من امتحان)</label>
        <select name="brandlist[]" multiple class="form-control">
            <?php
            if (!empty($exams)) {
                foreach ($exams as $exam) {
                    echo '<option value="' . $exam['id_ujian'] . '">' . $exam['nama_ujian'] . '</option>';
                }
            } else {
                echo '<option value="">لا توجد بيانات</option>';
            }
            ?>
        </select>
    </div>

    <label for="lecture">اختيار المحاضرة:</label>
    <select name="lecture[]" id="lecture" multiple onchange="fetchQuestions()">
        <?php foreach ($lectures as $lecture): ?>
            <option value="<?php echo htmlspecialchars($lecture['id']); ?>"><?php echo htmlspecialchars($lecture['Mada']); ?></option>
        <?php endforeach; ?>
    </select>

    <label for="questions">اختيار أسئلة المحاضرة:</label>
    <select name="questions[]" id="questions" multiple>
        <option value="">اختر سؤال</option>
    </select>

    <input type="submit" value="إرسال">
    	<a class="back-button" href="http://smartexams.whf.bz/ujian/master">رجوع</a>
</form>

</body>
</html>
