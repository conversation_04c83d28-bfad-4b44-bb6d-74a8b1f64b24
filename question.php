<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الإجابات</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #eef2f3;
            margin: 0;
            padding: 20px;
            direction: ltr;
            color: #333;
        }
        
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .container:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        h2 {
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
            position: relative;
        }

        h2 .back-button {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #fff;
            color: #4CAF50;
            border: 1px solid #4CAF50;
            padding: 5px 10px;
            border-radius: 5px;
            text-decoration: none;
            transition: all 0.3s;
        }

        h2 .back-button:hover {
            background-color: #4CAF50;
            color: #fff;
        }

        .question {
            margin-bottom: 40px;
            direction: ltr;
            background-color: #fafafa;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .question-text {
            margin-bottom: 10px;
            direction: ltr;
            text-align: left;
            font-size: 1.1em;
        }

        .options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            direction: ltr;
        }

        .option:hover {
            background-color: #f4f4f4;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .option-circle {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: #ccc;
            margin-right: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .option-letter {
            font-size: 1.2em;
            font-weight: bold;
        }

        .option-text {
            margin-right: 10px;
            direction: ltr;
            font-size: 1em;
            flex: 1;
        }

        .option-image {
            max-width: 100px;
            max-height: 100px;
            margin-right: 20px;
        }
        
        .question-image {
            max-width: 200px;
            max-height: 150px;
            margin-right: 30px;
        }

        .question-weight {
            margin-top: 5px;
            font-weight: bold;
            color: #4CAF50;
        }

        .correct-answer-text {
            margin-top: 5px;
            margin-right: 30px;
            color: green;
            font-weight: bold;
        }

        .correct-answer-circle {
            background-color: green;
            color: #fff;
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>بنك الأسئلة</h2>
<a href="javascript:history.back()" class="back-button">رجوع</a>
<br>

        <?php
        session_start();
        $con = mysqli_connect("localhost", "smarte10_exam", "mostafa900", "smarte10_exam");

        if (isset($_POST['save_multi_select'])) {
            $brands = $_POST['brandlist'];

            $pdo = new PDO("mysql:host=localhost;dbname=smarte10_exam", "smarte10_exam", "mostafa900");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->exec("set names utf8");

            $question_number = 1;

            foreach ($brands as $item) {
                $query_nama_ujian = "SELECT * FROM tb_soal WHERE id_soal = ?";
                $stmt_nama_ujian = $pdo->prepare($query_nama_ujian);
                $stmt_nama_ujian->execute([$item]);
                $tb_soal = $stmt_nama_ujian->fetch(PDO::FETCH_ASSOC);

                if ($stmt_nama_ujian->rowCount() == 0) {
                    echo "<p>لم يتم العثور على الامتحان المطلوب.</p>";
                    exit;
                } else {
        ?>
                    <div class="question">
                        <div class="question-text">
                            <p><?php echo $question_number; ?>) <?php echo $tb_soal['soal']; ?> (درجة السؤال <span class="question-weight"><?php echo $tb_soal['bobot']; ?></span>)</p>
                            <?php if ($tb_soal['file']) { ?>
                                <img src="uploads/bank_soal/<?php echo $tb_soal['file']; ?>" alt="صورة السؤال" class="question-image">
                            <?php } ?>
                        </div>
                        <div class="options">
                            <?php
                            $correct_answer = $tb_soal['jawaban'];

                            $options = array(
                                'A' => $tb_soal['A'],
                                'B' => $tb_soal['B'],
                                'C' => $tb_soal['C'],
                                'D' => $tb_soal['D'], 
                                'E' => $tb_soal['E'],
                                'F' => $tb_soal['F'],
                                'G' => $tb_soal['G'],
                                'H' => $tb_soal['H'], 
                                'I' => $tb_soal['I'],
                                'J' => $tb_soal['J']
                            );

                            foreach ($options as $key => $value) {
                                if (!empty($value)) { // تحقق إذا كان الخيار غير فارغ
                                    echo '<div class="option">';
                                    echo '<span class="option-circle ';
                                    if ($key === $correct_answer) {
                                        echo 'correct-answer-circle';
                                        echo '"><span class="option-letter">' . $key . '</span></span> <span class="option-text">' . $value . '</span> <span class="correct-answer-text">(الاجابة الصحيحة)</span>';
                                    } else {
                                        echo '"><span class="option-letter">' . $key . '</span></span> <span class="option-text">' . $value . '</span>';
                                    }
                                    if ($tb_soal['file_' . $key]) {
                                        echo '<img src="uploads/bank_soal/' . $tb_soal['file_' . $key] . '" alt="صورة الخيار" class="option-image">';
                                    }
                                    echo '</div>';
                                }
                            }
                            ?>
                        </div>
                    </div>
        <?php
                    $question_number++;
                }
            }
        }
        ?>
    </div>
<script>
    window.onload = function() {
        if (window.history && window.history.pushState) {
            window.history.pushState('forward', null, './#forward');
            $(window).on('popstate', function() {
                window.location.href = 'http://smartexams.whf.bz/ujian/questions';
            });
        }
    }
</script>
</body>
</html>